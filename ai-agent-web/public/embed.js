(function() {
  'use strict';
  
  const CONFIG_KEY = 'aiAgentConfig';
  const BUTTON_ID = 'ai-agent-button';
  const CONTAINER_ID = 'ai-agent-container';
  
  const config = window[CONFIG_KEY];
  let isVisible = false;
  let button = null;
  let container = null;
  let iframe = null;
  
  if (!config) {
    console.error('aiAgentConfig is not defined');
    return;
  }
  
  const baseUrl = config.baseUrl || window.location.origin;
  const theme = config.theme || {};
  const primaryColor = theme.primaryColor || '#3b82f6';
  const position = theme.position || 'bottom-right';
  
  // 创建悬浮按钮
  function createButton() {
    if (document.getElementById(BUTTON_ID)) return;
    
    button = document.createElement('div');
    button.id = BUTTON_ID;
    button.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2C6.48 2 2 6.48 2 12C2 13.54 2.36 15.01 3.01 16.31L2 22L7.69 20.99C8.99 21.64 10.46 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C10.74 20 9.54 19.75 8.47 19.3L6 20L6.7 17.53C6.25 16.46 6 15.26 6 14C6 8.48 8.48 6 12 6C15.52 6 18 8.48 18 12C18 15.52 15.52 18 12 18Z" fill="white"/>
      </svg>
    `;
    
    const buttonStyles = getButtonStyles();
    button.style.cssText = buttonStyles;
    button.addEventListener('click', togglePortal);
    
    document.body.appendChild(button);
  }
  
  // 创建门户容器
  function createContainer() {
    if (document.getElementById(CONTAINER_ID)) return;
    
    container = document.createElement('div');
    container.id = CONTAINER_ID;
    container.style.cssText = getContainerStyles();
    container.style.display = 'none';
    
    iframe = document.createElement('iframe');
    iframe.src = `${baseUrl}/portal`;
    iframe.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 12px;
    `;
    iframe.allow = 'microphone';
    
    container.appendChild(iframe);
    document.body.appendChild(container);
    
    // 监听iframe消息
    window.addEventListener('message', handleIframeMessage);
  }
  
  // 获取按钮样式
  function getButtonStyles() {
    const baseStyles = `
      position: fixed;
      width: 56px;
      height: 56px;
      background: ${primaryColor};
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 2147483647;
      transition: all 0.3s ease;
      user-select: none;
    `;
    
    const positionStyles = getPositionStyles('button');
    return baseStyles + positionStyles;
  }
  
  // 获取容器样式
  function getContainerStyles() {
    const baseStyles = `
      position: fixed;
      width: 380px;
      height: 600px;
      z-index: 2147483646;
      transition: all 0.3s ease;
      user-select: none;
    `;
    
    const positionStyles = getPositionStyles('container');
    return baseStyles + positionStyles;
  }
  
  // 获取位置样式
  function getPositionStyles(type) {
    const offset = type === 'button' ? '20px' : '20px';
    const containerOffset = type === 'container' ? '80px' : '20px';
    
    switch (position) {
      case 'bottom-right':
        return type === 'button' 
          ? `bottom: ${offset}; right: ${offset};`
          : `bottom: ${containerOffset}; right: ${offset};`;
      case 'bottom-left':
        return type === 'button'
          ? `bottom: ${offset}; left: ${offset};`
          : `bottom: ${containerOffset}; left: ${offset};`;
      case 'top-right':
        return type === 'button'
          ? `top: ${offset}; right: ${offset};`
          : `top: ${containerOffset}; right: ${offset};`;
      case 'top-left':
        return type === 'button'
          ? `top: ${offset}; left: ${offset};`
          : `top: ${containerOffset}; left: ${offset};`;
      default:
        return type === 'button'
          ? `bottom: ${offset}; right: ${offset};`
          : `bottom: ${containerOffset}; right: ${offset};`;
    }
  }
  
  // 切换门户显示状态
  function togglePortal() {
    if (!container) createContainer();
    
    isVisible = !isVisible;
    container.style.display = isVisible ? 'block' : 'none';
    
    // 更新按钮样式
    if (isVisible) {
      button.style.transform = 'scale(0.9)';
      button.style.background = '#1f2937';
    } else {
      button.style.transform = 'scale(1)';
      button.style.background = primaryColor;
    }
  }
  
  // 处理iframe消息
  function handleIframeMessage(event) {
    if (event.origin !== new URL(baseUrl).origin) return;
    
    if (event.data.type === 'ai-agent-ready') {
      // 发送配置到iframe
      iframe.contentWindow.postMessage({
        type: 'ai-agent-config',
        config: config
      }, baseUrl);
    }
    
    if (event.data.type === 'ai-agent-close') {
      togglePortal();
    }
  }
  
  // 响应式处理
  function handleResize() {
    if (!container) return;
    
    if (window.innerWidth <= 768) {
      container.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 2147483646;
        transition: all 0.3s ease;
      `;
    } else {
      container.style.cssText = getContainerStyles();
    }
  }
  
  // 初始化
  function init() {
    createButton();
    window.addEventListener('resize', handleResize);
    
    // 添加悬停效果
    if (button) {
      button.addEventListener('mouseenter', () => {
        if (!isVisible) {
          button.style.transform = 'scale(1.1)';
        }
      });
      
      button.addEventListener('mouseleave', () => {
        if (!isVisible) {
          button.style.transform = 'scale(1)';
        }
      });
    }
  }
  
  // 确保DOM加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
  
  // 暴露全局方法
  window.aiAgent = {
    show: () => {
      if (!isVisible) togglePortal();
    },
    hide: () => {
      if (isVisible) togglePortal();
    },
    toggle: togglePortal
  };
  
})();