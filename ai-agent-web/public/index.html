<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Agent 集成测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1f2937;
            margin-bottom: 20px;
        }
        .demo-content {
            line-height: 1.6;
            color: #374151;
        }
        .highlight {
            background: #fef3c7;
            padding: 2px 6px;
            border-radius: 4px;
        }
        pre {
            background: #f3f4f6;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
    <script>
        window.aiAgentConfig = {
            baseUrl: 'http://localhost:3000',
            theme: {
                primaryColor: '#3b82f6',
                position: 'bottom-right'
            }
        }
    </script>
    <script src="http://localhost:3000/embed.min.js" defer></script>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Agent 门户集成演示</h1>
        <div class="demo-content">
            <p>欢迎使用AI Agent门户系统！</p>
            <p>这是一个演示页面，展示如何在外部网站中集成AI Agent门户。</p>
            <p>请注意页面<span class="highlight">右下角的AI图标</span>，点击即可打开AI助手对话框。</p>

            <h3>功能特点：</h3>
            <ul>
                <li>🎯 一键集成，只需引入embed.js</li>
                <li>🎨 可自定义主题颜色和位置</li>
                <li>📱 响应式设计，支持移动端</li>
                <li>🔗 支持多个Dify应用集成</li>
                <li>⚡ 轻量级，不影响页面性能</li>
            </ul>

            <h3>使用方法：</h3>
            <pre>&lt;script&gt;
window.aiAgentConfig = {
    baseUrl: 'http://localhost:3000',
    theme: {
        primaryColor: '#3b82f6',
        position: 'bottom-right'
    }
}
&lt;/script&gt;
&lt;script src="http://localhost:3000/embed.js" defer&gt;&lt;/script&gt;</pre>
        </div>
    </div>
</body>
</html>