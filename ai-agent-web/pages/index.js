import { useState } from 'react'
import Head from 'next/head'

export default function Home() {
  const [showDemo, setShowDemo] = useState(false)

  return (
    <>
      <Head>
        <title>AI Agent Portal</title>
        <meta name="description" content="AI Agent Portal System" />
      </Head>

      <main style={{ padding: '40px', fontFamily: 'system-ui' }}>
        <h1>AI Agent Portal System</h1>
        
        <section style={{ marginBottom: '40px' }}>
          <h2>集成说明</h2>
          <p>在您的网站中添加以下代码即可集成AI Agent门户：</p>
          
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '16px', 
            borderRadius: '8px',
            overflow: 'auto'
          }}>
{`<script>
window.aiAgentConfig = {
  baseUrl: '${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}',
  theme: {
    primaryColor: '#3b82f6',
    position: 'bottom-right'
  }
}
</script>
<script src="${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}/embed.js" defer></script>`}
          </pre>
        </section>

        <section>
          <h2>效果演示</h2>
          <button 
            onClick={() => setShowDemo(!showDemo)}
            style={{
              padding: '12px 24px',
              background: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            {showDemo ? '关闭演示' : '开启演示'}
          </button>
          
          {showDemo && (
            <div style={{ marginTop: '20px' }}>
              <p>演示模式已开启，请查看页面右下角的AI助手图标</p>
              <script
                dangerouslySetInnerHTML={{
                  __html: `
                    window.aiAgentConfig = {
                      baseUrl: '${typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'}',
                      theme: {
                        primaryColor: '#3b82f6',
                        position: 'bottom-right'
                      }
                    };
                    if (!document.getElementById('ai-agent-embed')) {
                      const script = document.createElement('script');
                      script.id = 'ai-agent-embed';
                      script.src = '/embed.min.js';
                      script.defer = true;
                      document.head.appendChild(script);
                    }
                  `
                }}
              />
            </div>
          )}
        </section>
      </main>
    </>
  )
}