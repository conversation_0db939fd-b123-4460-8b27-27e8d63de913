import { useEffect, useState } from 'react'

export default function Portal() {
  const [mounted, setMounted] = useState(false)
  const [selectedAgent, setSelectedAgent] = useState(null)
  const [config, setConfig] = useState(null)

  const agents = [
    {
      id: 'customer-service',
      name: '客服助手',
      description: '专业的客户服务支持',
      difyUrl: 'http://************:9009/chatbot/05VnYqM6Gz8fbnrl'
    },
    {
      id: 'sales-assistant',
      name: '销售顾问',
      description: '产品咨询和销售支持',
      difyUrl: 'http://************:9009/chatbot/05VnYqM6Gz8fbnrl'
    }
  ]

  useEffect(() => {
    setMounted(true)

    // 监听来自父窗口的消息
    const handleMessage = (event) => {
      if (event.data.type === 'ai-agent-config') {
        console.log('Received config:', event.data.config)
        setConfig(event.data.config)
      }
    }

    window.addEventListener('message', handleMessage)

    // 通知父窗口iframe已准备就绪
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'ai-agent-ready'
      }, '*')
    }

    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [])

  const handleClose = () => {
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'ai-agent-close'
      }, '*')
    }
  }

  const selectAgent = (agent) => {
    setSelectedAgent(agent)
  }

  const goBack = () => {
    setSelectedAgent(null)
  }

  if (!mounted) {
    return <div>Loading...</div>
  }

  return (
    <div style={{
      width: '100%',
      height: '100%',
      background: 'white',
      borderRadius: '0',
      boxShadow: 'none',
      display: 'flex',
      flexDirection: 'column',
      fontFamily: '-apple-system, BlinkMacSystemFont, sans-serif',
      borderLeft: '1px solid #e5e7eb'
    }}>
      {/* Header */}
      <div style={{
        padding: '16px 20px',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h2 style={{ margin: 0, fontSize: '18px', color: '#1f2937' }}>
          AI Agent 助手
        </h2>
        <button
          onClick={handleClose}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '24px',
            cursor: 'pointer',
            color: '#6b7280',
            padding: 0,
            width: '24px',
            height: '24px'
          }}
        >
          ×
        </button>
      </div>

      {/* Content */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        {!selectedAgent ? (
          <AgentList agents={agents} onSelect={selectAgent} />
        ) : (
          <AgentChat agent={selectedAgent} onBack={goBack} />
        )}
      </div>
    </div>
  )
}

// Agent List Component
function AgentList({ agents, onSelect }) {
  return (
    <div style={{ padding: '20px' }}>
      <h3 style={{ margin: '0 0 16px 0', color: '#374151' }}>选择AI助手</h3>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        {agents.map(agent => (
          <div
            key={agent.id}
            onClick={() => onSelect(agent)}
            style={{
              padding: '16px',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.2s',
              backgroundColor: '#f9fafb'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#f3f4f6'
              e.target.style.borderColor = '#d1d5db'
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#f9fafb'
              e.target.style.borderColor = '#e5e7eb'
            }}
          >
            <h4 style={{ margin: '0 0 8px 0', color: '#1f2937' }}>{agent.name}</h4>
            <p style={{ margin: 0, color: '#6b7280', fontSize: '14px' }}>{agent.description}</p>
          </div>
        ))}
      </div>
    </div>
  )
}

// Agent Chat Component
function AgentChat({ agent, onBack }) {
  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Chat Header */}
      <div style={{
        padding: '12px 20px',
        borderBottom: '1px solid #e5e7eb',
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }}>
        <button
          onClick={onBack}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '18px',
            cursor: 'pointer',
            color: '#6b7280',
            padding: '4px'
          }}
        >
          ←
        </button>
        <h3 style={{ margin: 0, color: '#1f2937' }}>{agent.name}</h3>
      </div>

      {/* Chat Content */}
      <div style={{ flex: 1, position: 'relative' }}>
        <iframe
          src={agent.difyUrl}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            borderRadius: '0'
          }}
          allow="microphone"
        />
      </div>
    </div>
  )
}