name: Build and Push API & Web

on:
  push:
    branches:
      - "main"
      - "deploy/dev"
      - "deploy/enterprise"
      - "build/**"
    tags:
      - "*"

concurrency:
  group: build-push-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  DOCKERHUB_USER: ${{ secrets.DOCKERHUB_USER }}
  DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
  DIFY_WEB_IMAGE_NAME: ${{ vars.DIFY_WEB_IMAGE_NAME || 'langgenius/dify-web' }}
  DIFY_API_IMAGE_NAME: ${{ vars.DIFY_API_IMAGE_NAME || 'langgenius/dify-api' }}

jobs:
  build:
    runs-on: ${{ matrix.platform == 'linux/arm64' && 'arm64_runner' || 'ubuntu-latest' }}
    if: github.repository == 'langgenius/dify'
    strategy:
      matrix:
        include:
          - service_name: "build-api-amd64"
            image_name_env: "DIFY_API_IMAGE_NAME"
            context: "api"
            platform: linux/amd64
          - service_name: "build-api-arm64"
            image_name_env: "DIFY_API_IMAGE_NAME"
            context: "api"
            platform: linux/arm64
          - service_name: "build-web-amd64"
            image_name_env: "DIFY_WEB_IMAGE_NAME"
            context: "web"
            platform: linux/amd64
          - service_name: "build-web-arm64"
            image_name_env: "DIFY_WEB_IMAGE_NAME"
            context: "web"
            platform: linux/arm64

    steps:
      - name: Prepare
        run: |
          platform=${{ matrix.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKERHUB_USER }}
          password: ${{ env.DOCKERHUB_TOKEN }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env[matrix.image_name_env] }}

      - name: Build Docker image
        id: build
        uses: docker/build-push-action@v6
        with:
          context: "{{defaultContext}}:${{ matrix.context }}"
          platforms: ${{ matrix.platform }}
          build-args: COMMIT_SHA=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.revision'] }}
          labels: ${{ steps.meta.outputs.labels }}
          outputs: type=image,name=${{ env[matrix.image_name_env] }},push-by-digest=true,name-canonical=true,push=true
          cache-from: type=gha,scope=${{ matrix.service_name }}
          cache-to: type=gha,mode=max,scope=${{ matrix.service_name }}

      - name: Export digest
        env:
          DIGEST: ${{ steps.build.outputs.digest }}
        run: |
          mkdir -p /tmp/digests
          sanitized_digest=${DIGEST#sha256:}
          touch "/tmp/digests/${sanitized_digest}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ matrix.context }}-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1

  create-manifest:
    needs: build
    runs-on: ubuntu-latest
    if: github.repository == 'langgenius/dify'
    strategy:
      matrix:
        include:
          - service_name: "merge-api-images"
            image_name_env: "DIFY_API_IMAGE_NAME"
            context: "api"
          - service_name: "merge-web-images"
            image_name_env: "DIFY_WEB_IMAGE_NAME"
            context: "web"
    steps:
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digests-${{ matrix.context }}-*
          merge-multiple: true

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKERHUB_USER }}
          password: ${{ env.DOCKERHUB_TOKEN }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env[matrix.image_name_env] }}
          tags: |
            type=raw,value=latest,enable=${{ startsWith(github.ref, 'refs/tags/') && !contains(github.ref, '-') }}
            type=ref,event=branch
            type=sha,enable=true,priority=100,prefix=,suffix=,format=long
            type=raw,value=${{ github.ref_name }},enable=${{ startsWith(github.ref, 'refs/tags/') }}

      - name: Create manifest list and push
        working-directory: /tmp/digests
        env:
          IMAGE_NAME: ${{ env[matrix.image_name_env] }}
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf "$IMAGE_NAME@sha256:%s " *)

      - name: Inspect image
        env:
          IMAGE_NAME: ${{ env[matrix.image_name_env] }}
          IMAGE_VERSION: ${{ steps.meta.outputs.version }}
        run: |
          docker buildx imagetools inspect "$IMAGE_NAME:$IMAGE_VERSION"
